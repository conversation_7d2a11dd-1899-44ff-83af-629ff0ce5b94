<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                xmlns:rx="http://xmlns.jcp.org/jsf/composite/component">

    <!-- 学生検索画面選択結果取得 -->
    <p:remoteCommand name="t3_receiveGaksekiCd" actionListener="#{xgm00301T03Bean.doReceiveGaksekiCd()}" process="@this " update="t3_gaksekiCd t3_gakseiName" />
    
    <section>
        <p:outputPanel id="t3_mainCondition">

            <dl class="inputArea">
                <!-- 学籍番号 -->
                <div class="itemTable">
                    <dt class="hissu">
                        <p:outputLabel for="t3_gaksekiCd" value="#{item['pkbGakReki.gaksekiCd.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <!-- 検索ボタン -->
                        <p:commandButton styleClass="inTableBtnIconAlt"
                                         id="t3_search" icon="fa fa-fw fa-search"
                                         actionListener="#{xgm00301T03Bean.doOpenSearchGakseki}"
                                         process="@this"
                                         disabled="#{!xgm00301T03Bean.payList.isEmpty()}" /> 

                        <p:inputText id="t3_gaksekiCd" widgetVar="t3_gaksekiCd" styleClass="inputHalfSize10"
                                     value="#{xgm00301T03Bean.condition.gaksekiCd}" disabled="#{!xgm00301T03Bean.payList.isEmpty()}">
                            <p:clientValidator event="blur" />
                            <p:ajax event="change" process="t3_gaksekiCd" update="t3_gakseiName"
                                    listener="#{xgm00301T03Bean.doGetGakName()}" />
                        </p:inputText>
                        <p:message for="t3_gaksekiCd" display="tooltip" />
                    </dd>
                </div>
                <!-- 振込依頼人コード -->
                <div class="itemTable">
                    <dt>
                        <p:outputLabel for="t3_furikomiIraiCd" value="#{item['xgm004.furikomiIraiCd.0.label']}" />  
                    </dt>
                    <dd>
                        <p:inputText id="t3_furikomiIraiCd" widgetVar="t3_furikomiIraiCd" value="#{xgm00301T03Bean.condition.furikomiIraiCd}" styleClass="inputHalfSize10" >
                            <p:clientValidator event="blur" />
                        </p:inputText>
                    </dd>

                    <!-- 選択ボタン -->
                    <p:commandButton styleClass="inTableBtn letterSpaceHalf"
                                        value="#{btn['button.012']}" rendered="#{xgm00301T03Bean.payList.isEmpty()}"
                                        actionListener="#{xgm00301Bean.doSearchPayWList}"
                                        process="@this funcForm:mainCondition funcForm:tabArea:t3_mainCondition"
                                        update="@this funcForm:tabArea:t3_mainCondition" />
                    <!-- 解除ボタン -->
                    <p:commandButton styleClass="inTableBtn letterSpaceHalf"
                                        value="#{btn['button.013']}" rendered="#{!xgm00301T03Bean.payList.isEmpty()}"
                                        actionListener="#{xgm00301T03Bean.doClear}"
                                        process="@this t3_gaksekiCd" update="@this funcForm:tabArea:t3_mainCondition" />
                </div>
                <!-- 学生氏名 -->
                 <div class="itemTable">    
                    <dt>
                        <p:outputLabel value="#{item['common.gakseiName.0.label']}" />  
                    </dt>
                    <dd>
                        <h:outputText id="t3_gakseiName" value="#{xgm00301T03Bean.gakseiName}" />
                    </dd>
                </div>
            </dl>
            <dl>
                <div>
                    <p class="countData" >
                        <h:outputText id="targetCount" value="#{xgm00301T03Bean.payList.size()}"/>
                        <h:outputText value="#{item['common.tani.ken.0.label']}" />
                    </p>
                    <!-- 納付金リスト -->
                    <p:dataTable id="t3_payList" var="data" value="#{xgm00301T03Bean.payList}"
                                 selection="#{xgm00301T03Bean.selectedPayList}" rowKey="#{data.hashCode()}"
                                 paginator="false" scrollable="true" resizableColumns="false" scrollHeight="280" >

                        <!-- チェックボックス -->
                        <p:column selectionMode="multiple" class="colSizeCheckbox alignCenter" />

                        <!-- 割当年度 -->
                        <p:column id="t3_colNendo" headerText="#{item['ghePaywBun.nendo.0.label']}"
                                  sortBy="#{data.nendo}" styleClass="colSize6 alignLeft">
                            <h:outputText value="#{data.nendo}" />
                        </p:column>

                        <!-- 納付金コード -->
                        <p:column id="t3_colpayCd" headerText="#{item['ghePaywBun.payCd.0.label']}"
                                  sortBy="#{data.payCd}" styleClass="colSize8 alignLeft">
                            <h:outputText value="#{data.payCd}" />
                        </p:column>

                        <!-- パターンコード -->
                        <p:column id="t3_colPatternCd" headerText="#{item['ghePaywBun.patternCd.0.shortLabel']}"
                                  sortBy="#{data.patternCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{data.patternCd}" />
                        </p:column>
                        
                        <!-- 分納区分コード -->
                        <p:column id="t3_colBunnoKbnCd" headerText="#{item['ghePaywBun.bunnoKbnCd.0.shortLabel']}"
                                  sortBy="#{data.bunnoKbnCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{xgm00301Bean.doJoinCodeName(data.bunnoKbnCd,data.bunnoKbnName)}" />
                        </p:column>

                        <!-- 納付金名称 -->
                        <p:column id="t3_colPayName" headerText="#{item['ghcPayh.payName.0.label']}"
                                  sortBy="#{data.payName}"
                                  styleClass="colSize15 alignLeft">
                            <h:outputText value="#{data.payName}" />
                        </p:column>

                        <!-- 分割NO -->
                        <p:column id="t3_colBunkatsuNo" headerText="#{item['ghePaywBun.bunkatsuNo.0.label']}"
                                  sortBy="#{data.bunkatsuNo}"
                                  styleClass="colSize6 alignRight">
                            <h:outputText value="#{data.bunkatsuNo}" />
                        </p:column>
                        
                        <!-- 納入期限 -->
                        <p:column id="t3_colPayLimit" headerText="#{item['ghePaywBun.payLimitDate.0.shortLabel']}"
                                  sortBy="#{data.payLimitDate}"
                                  styleClass="colSize6 alignLeft" >
                            <rx:outputDateText value="#{data.payLimitDate}"
                                               datePattern="#{format['date.02']}" yobiPattern="none" timePattern="none"/>
                        </p:column>
                        
                        <!-- 出力日 -->
                        <p:column id="t3_colOutputDate" headerText="#{item['ghd008.outputDate.0.label']}"
                                  sortBy="#{data.outputDate}"
                                  styleClass="colSize6 alignLeft" >
                            <rx:outputDateText value="#{data.outputDate}"
                                               datePattern="#{format['date.02']}" yobiPattern="none" timePattern="none"/>
                        </p:column>

                    </p:dataTable>
                </div>
            </dl>
        </p:outputPanel>
    </section>

    <!--メインボタン-->
    <div class="btnAreaFuncBottom">
        <!--発行する
        <rx:asyncExecRequest id="t3_outputPrint" compoAsyncId="outputPrintBtn3"
                             process="@this funcForm:mainCondition funcForm:tabArea:t3_mainCondition @(.gkOrdDesignation)"
                             asyncExecReqDto="#{xgm00301T03Bean.collectiveOutputData}"
                             update="@this funcForm:mainCondition funcForm:tabArea:t3_mainCondition @(.gkOrdDesignation)"
                             beforeMethod="#{xgm00301Bean.doOutputPrint(3)}"
                             buttonName="#{btn['button.802']}" buttonStyleClass="btnFileoutput letterSpaceHalf" />-->
    </div>
</ui:composition>